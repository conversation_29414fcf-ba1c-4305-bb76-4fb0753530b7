// @ts-check
import { defineConfig } from 'astro/config';

import tailwindcss from '@tailwindcss/vite';

// https://astro.build/config
export default defineConfig({
  i18n: {
    defaultLocale: 'zh',
    locales: ['zh', 'en'],
    routing: {
      prefixDefaultLocale: false
    }
  },
  server: {
    host: true, // 允许外网访问
    port: 4321  // 指定端口
  },
  vite: {
    plugins: [tailwindcss()],
    server: {
      host: '0.0.0.0', // 监听所有网络接口
      port: 4321
    }
  }
});
