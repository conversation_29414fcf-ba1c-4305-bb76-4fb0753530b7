export const en = {
  // Navigation
  nav: {
    home: 'Home',
    about: 'About',
    contact: 'Contact',
    website: 'My Website'
  },

  // Theme
  theme: {
    light: 'Light Theme',
    dark: 'Dark Theme',
    system: 'Follow System',
    toggle: 'Theme Toggle'
  },

  // Language
  language: {
    chinese: '中文',
    english: 'English',
    system: 'Follow System',
    switch: 'Language Switch'
  },

  // Footer
  footer: {
    home: 'Home',
    about: 'About Us',
    contact: 'Contact Us',
    privacy: 'Privacy Policy',
    copyright: 'Copyright © 2024 - Powered by Astro + Tailwind CSS + daisyUI + Alpine.js',
    email: 'Email',
    website: 'Website',
    external: 'External Link'
  },

  // Home page
  home: {
    title: 'Welcome to My Website',
    description: 'A modern website built with Astro, Tailwind CSS, daisyUI, and Alpine.js',
    hero: {
      title: 'Hello World!',
      subtitle: 'Welcome to a modern website built with Astro, Tailwind CSS, daisyUI, and Alpine.js. This is a powerful and beautiful frontend framework combination.',
      cta: 'Start Exploring'
    },
    features: {
      title: 'Technical Features',
      subtitle: 'Modern frontend technology stack',
      astro: {
        title: 'Astro',
        description: 'Modern static site generator with zero JavaScript runtime'
      },
      tailwind: {
        title: 'Tailwind CSS',
        description: 'Utility-first CSS framework for rapid modern interface development'
      },
      daisyui: {
        title: 'daisyUI',
        description: 'Component library based on Tailwind CSS, beautiful and easy to use'
      },
      alpine: {
        title: 'Alpine.js',
        description: 'Lightweight JavaScript framework for simple reactive interactions'
      }
    },
    demo: {
      title: 'Interactive Demo',
      subtitle: 'Experience Alpine.js reactive functionality',
      counter: {
        title: 'Counter Demo',
        reset: 'Reset'
      },
      todo: {
        title: 'Todo Demo',
        placeholder: 'Add new task...',
        add: 'Add',
        delete: 'Delete',
        items: ['Learn Astro', 'Configure Tailwind CSS', 'Integrate Alpine.js']
      }
    }
  },

  // About page
  about: {
    title: 'About Us',
    description: 'Learn about our technology stack and development philosophy',
    header: {
      title: 'About Us',
      subtitle: 'Learn about our technology stack and development philosophy'
    },
    intro: {
      title: 'Our Mission',
      content: 'We are committed to building modern, high-performance websites using the latest frontend technologies. By combining Astro, Tailwind CSS, daisyUI, and Alpine.js, we create user experiences that are both beautiful and practical.'
    },
    tech: {
      title: 'Technology Stack Details',
      astro: {
        title: 'Astro',
        description: 'Astro is a modern static site generator focused on performance and developer experience. It supports multiple frontend frameworks and generates zero-JavaScript static pages by default.'
      },
      tailwind: {
        title: 'Tailwind CSS',
        description: 'Tailwind CSS is a utility-first CSS framework that provides numerous utility classes, allowing us to quickly build custom designs without writing custom CSS.'
      },
      daisyui: {
        title: 'daisyUI',
        description: 'daisyUI is a component library based on Tailwind CSS that provides semantic class names and pre-designed components for more efficient development.'
      },
      alpine: {
        title: 'Alpine.js',
        description: 'Alpine.js is a lightweight JavaScript framework that provides Vue.js-like reactive and declarative features but with a smaller footprint, making it more suitable for progressive enhancement.'
      }
    },
    features: {
      title: 'Framework Features',
      easy: {
        title: 'Easy to Use',
        description: 'Simple configuration, quick to get started'
      },
      production: {
        title: 'Production Ready',
        description: 'Tested and stable technology stack'
      },
      scalable: {
        title: 'Scalable',
        description: 'Supports various plugins and extensions'
      }
    }
  },

  // Contact page
  contact: {
    title: 'Contact Us',
    description: 'Get in touch with us for technical support and consultation',
    header: {
      title: 'Contact Us',
      subtitle: 'We\'d love to hear from you'
    },
    form: {
      title: 'Send Message',
      name: 'Name',
      namePlaceholder: 'Please enter your name',
      email: 'Email',
      emailPlaceholder: 'Please enter your email',
      subject: 'Subject',
      subjectPlaceholder: 'Please enter message subject',
      message: 'Message',
      messagePlaceholder: 'Please enter your message...',
      send: 'Send Message'
    },
    info: {
      title: 'Contact Information',
      address: {
        title: 'Address',
        value: 'Beijing, China'
      },
      phone: {
        title: 'Phone',
        value: '+86 138 0000 0000'
      },
      email: {
        title: 'Email',
        value: '<EMAIL>'
      }
    },
    social: {
      title: 'Follow Us',
      social: 'Social Media'
    }
  }
} as const;
