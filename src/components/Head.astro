---
import { getTranslations, defaultLocale } from '../i18n';

export interface Props {
  title?: string;
  description?: string;
}

const currentLocale = defaultLocale;
const t = getTranslations(currentLocale);

const {
  title = t.meta.title,
  description = t.meta.description
} = Astro.props;
---

<head>
  <meta charset="UTF-8" />
  <meta name="description" content={description} />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <meta name="generator" content={Astro.generator} />
  <title>{title}</title>

  <!-- Alpine.js -->
  <script>
    import Alpine from 'alpinejs'
    window.Alpine = Alpine
    Alpine.start()
  </script>
</head>
