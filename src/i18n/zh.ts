export const zh = {
  // Navigation
  nav: {
    home: '首页',
    about: '关于',
    contact: '联系',
    website: '我的网站'
  },

  // Theme
  theme: {
    light: '浅色主题',
    dark: '深色主题',
    system: '跟随系统',
    toggle: '主题切换'
  },

  // Language
  language: {
    chinese: '中文',
    english: 'English',
    system: '跟随系统',
    switch: '语言切换'
  },

  // Footer
  footer: {
    home: '首页',
    about: '关于我们',
    contact: '联系我们',
    privacy: '隐私政策',
    copyright: '版权所有 © 2024 - 由 Astro + Tailwind CSS + daisyUI + Alpine.js 强力驱动',
    email: '邮箱',
    website: '网站',
    external: '外部链接'
  },

  // Home page
  home: {
    title: '欢迎来到我的网站',
    description: '使用 Astro、Tailwind CSS、daisyUI 和 Alpine.js 构建的现代网站',
    hero: {
      title: '你好世界！',
      subtitle: '欢迎来到使用 Astro、Tailwind CSS、daisyUI 和 Alpine.js 构建的现代网站。这是一个功能强大且美观的前端框架组合。',
      cta: '开始探索'
    },
    features: {
      title: '技术特性',
      subtitle: '现代化的前端技术栈',
      astro: {
        title: 'Astro',
        description: '现代静态站点生成器，零 JavaScript 运行时'
      },
      tailwind: {
        title: 'Tailwind CSS',
        description: '实用优先的 CSS 框架，快速构建现代界面'
      },
      daisyui: {
        title: 'daisyUI',
        description: '基于 Tailwind CSS 的组件库，美观易用'
      },
      alpine: {
        title: 'Alpine.js',
        description: '轻量级 JavaScript 框架，简单的响应式交互'
      }
    },
    demo: {
      title: '交互演示',
      subtitle: '体验 Alpine.js 的响应式功能',
      counter: {
        title: '计数器演示',
        reset: '重置'
      },
      todo: {
        title: '待办事项演示',
        placeholder: '添加新任务...',
        add: '添加',
        delete: '删除',
        items: ['学习 Astro', '配置 Tailwind CSS', '集成 Alpine.js']
      }
    }
  },

  // About page
  about: {
    title: '关于我们',
    description: '了解我们的技术栈和开发理念',
    header: {
      title: '关于我们',
      subtitle: '了解我们的技术栈和开发理念'
    },
    intro: {
      title: '我们的使命',
      content: '我们致力于使用最新的前端技术构建现代化、高性能的网站。通过结合 Astro、Tailwind CSS、daisyUI 和 Alpine.js，我们创造出既美观又实用的用户体验。'
    },
    tech: {
      title: '技术栈详情',
      astro: {
        title: 'Astro',
        description: 'Astro 是一个现代的静态站点生成器，专注于性能和开发者体验。它支持多种前端框架，并且默认生成零 JavaScript 的静态页面。'
      },
      tailwind: {
        title: 'Tailwind CSS',
        description: 'Tailwind CSS 是一个实用优先的 CSS 框架，提供了大量的实用类，让我们能够快速构建自定义设计，而无需编写自定义 CSS。'
      },
      daisyui: {
        title: 'daisyUI',
        description: 'daisyUI 是基于 Tailwind CSS 的组件库，提供了语义化的类名和预设计的组件，让开发更加高效。'
      },
      alpine: {
        title: 'Alpine.js',
        description: 'Alpine.js 是一个轻量级的 JavaScript 框架，提供了类似 Vue.js 的响应式和声明式特性，但体积更小，更适合渐进式增强。'
      }
    },
    features: {
      title: '框架特性',
      easy: {
        title: '易于使用',
        description: '简单的配置，快速上手'
      },
      production: {
        title: '生产就绪',
        description: '经过测试的稳定技术栈'
      },
      scalable: {
        title: '可扩展',
        description: '支持各种插件和扩展'
      }
    }
  },

  // Contact page
  contact: {
    title: '联系我们',
    description: '与我们取得联系，获取技术支持和咨询',
    header: {
      title: '联系我们',
      subtitle: '我们很乐意听到您的声音'
    },
    form: {
      title: '发送消息',
      name: '姓名',
      namePlaceholder: '请输入您的姓名',
      email: '邮箱',
      emailPlaceholder: '请输入您的邮箱',
      subject: '主题',
      subjectPlaceholder: '请输入消息主题',
      message: '消息',
      messagePlaceholder: '请输入您的消息内容...',
      send: '发送消息'
    },
    info: {
      title: '联系信息',
      address: {
        title: '地址',
        value: '中国，北京市'
      },
      phone: {
        title: '电话',
        value: '+86 138 0000 0000'
      },
      email: {
        title: '邮箱',
        value: '<EMAIL>'
      }
    },
    social: {
      title: '关注我们',
      social: '社交媒体'
    }
  }
} as const;
