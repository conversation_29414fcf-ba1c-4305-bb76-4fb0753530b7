---
import Layout from '../layouts/Layout.astro';

// Import Lucide icons
import { MapPin, Phone, Mail, ExternalLink } from '@lucide/astro';

// Import i18n utilities
import { getCurrentLocale, getTranslations } from '../i18n';

// Get current locale and translations
const currentLocale = getCurrentLocale();
const t = getTranslations(currentLocale);
---

<Layout title={t.contact.title} description={t.contact.description}>
  <div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="text-center mb-16">
      <h1 class="text-5xl font-bold mb-4">{t.contact.header.title}</h1>
      <p class="text-xl text-base-content/70">{t.contact.header.subtitle}</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Contact Form -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title text-2xl mb-6">{t.contact.form.title}</h2>

          <form x-data="{
            name: '',
            email: '',
            message: '',
            submitted: false,
            submitForm() {
              // 这里可以添加实际的表单提交逻辑
              this.submitted = true;
              setTimeout(() => {
                this.submitted = false;
                this.name = '';
                this.email = '';
                this.message = '';
              }, 3000);
            }
          }" @submit.prevent="submitForm()">

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text">{t.contact.form.name}</span>
              </label>
              <input
                type="text"
                placeholder={t.contact.form.namePlaceholder}
                class="input input-bordered w-full"
                x-model="name"
                required
              />
            </div>

            <div class="form-control mb-4">
              <label class="label">
                <span class="label-text">{t.contact.form.email}</span>
              </label>
              <input
                type="email"
                placeholder={t.contact.form.emailPlaceholder}
                class="input input-bordered w-full"
                x-model="email"
                required
              />
            </div>

            <div class="form-control mb-6">
              <label class="label">
                <span class="label-text">{t.contact.form.message}</span>
              </label>
              <textarea
                class="textarea textarea-bordered h-32"
                placeholder={t.contact.form.messagePlaceholder}
                x-model="message"
                required
              ></textarea>
            </div>

            <div class="form-control">
              <button
                type="submit"
                class="btn btn-primary"
                :class="{ 'btn-success': submitted }"
                :disabled="submitted"
              >
                <span x-show="!submitted">{t.contact.form.send}</span>
                <span x-show="submitted">✓ 已发送</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="space-y-6">
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-2xl mb-6">{t.contact.info.title}</h2>

            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                  <MapPin class="w-6 h-6 text-primary-content" />
                </div>
                <div>
                  <h3 class="font-semibold">{t.contact.info.address.title}</h3>
                  <p class="text-base-content/70">{t.contact.info.address.value}</p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-secondary rounded-full flex items-center justify-center">
                  <Phone class="w-6 h-6 text-secondary-content" />
                </div>
                <div>
                  <h3 class="font-semibold">{t.contact.info.phone.title}</h3>
                  <p class="text-base-content/70">{t.contact.info.phone.value}</p>
                </div>
              </div>

              <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-accent rounded-full flex items-center justify-center">
                  <Mail class="w-6 h-6 text-accent-content" />
                </div>
                <div>
                  <h3 class="font-semibold">{t.contact.info.email.title}</h3>
                  <p class="text-base-content/70">{t.contact.info.email.value}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Links -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">关注我们</h2>
            <div class="flex space-x-4">
              <a href="#" class="btn btn-circle btn-outline" title="社交媒体">
                <ExternalLink class="w-5 h-5" />
              </a>
              <a href="#" class="btn btn-circle btn-outline" title="社交媒体">
                <ExternalLink class="w-5 h-5" />
              </a>
              <a href="#" class="btn btn-circle btn-outline" title="社交媒体">
                <ExternalLink class="w-5 h-5" />
              </a>
              <a href="#" class="btn btn-circle btn-outline" title="社交媒体">
                <ExternalLink class="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>

        <!-- FAQ -->
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-xl mb-4">常见问题</h2>
            <div class="collapse collapse-arrow bg-base-200 mb-2">
              <input type="radio" name="faq-accordion" checked="checked" />
              <div class="collapse-title text-lg font-medium">
                如何开始使用这个框架？
              </div>
              <div class="collapse-content">
                <p>您可以克隆这个项目，然后运行 npm install 安装依赖，最后运行 npm run dev 启动开发服务器。</p>
              </div>
            </div>
            <div class="collapse collapse-arrow bg-base-200 mb-2">
              <input type="radio" name="faq-accordion" />
              <div class="collapse-title text-lg font-medium">
                支持哪些浏览器？
              </div>
              <div class="collapse-content">
                <p>支持所有现代浏览器，包括 Chrome、Firefox、Safari 和 Edge 的最新版本。</p>
              </div>
            </div>
            <div class="collapse collapse-arrow bg-base-200">
              <input type="radio" name="faq-accordion" />
              <div class="collapse-title text-lg font-medium">
                如何自定义主题？
              </div>
              <div class="collapse-content">
                <p>您可以在 tailwind.config.js 文件中修改 daisyUI 的主题配置，或者创建自定义的 CSS 变量。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>
