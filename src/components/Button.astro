---
export interface Props {
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'link' | 'outline';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  href?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  class?: string;
}

const { 
  variant = 'primary', 
  size = 'md', 
  href, 
  type = 'button', 
  disabled = false,
  class: className = ''
} = Astro.props;

const baseClasses = 'btn';
const variantClasses = {
  primary: 'btn-primary',
  secondary: 'btn-secondary',
  accent: 'btn-accent',
  ghost: 'btn-ghost',
  link: 'btn-link',
  outline: 'btn-outline'
};
const sizeClasses = {
  xs: 'btn-xs',
  sm: 'btn-sm',
  md: '',
  lg: 'btn-lg'
};

const classes = [
  baseClasses,
  variantClasses[variant],
  sizeClasses[size],
  className
].filter(Boolean).join(' ');
---

{href ? (
  <a href={href} class={classes}>
    <slot />
  </a>
) : (
  <button type={type} disabled={disabled} class={classes}>
    <slot />
  </button>
)}
