---
import Layout from '../layouts/Layout.astro';

// Import Lucide icons
import { Rocket, Palette, Component, Zap } from '@lucide/astro';

// Import i18n utilities
import { getTranslations, defaultLocale } from '../i18n';

// Use default locale for server-side rendering
const currentLocale = defaultLocale;
const t = getTranslations(currentLocale);
---

<Layout title={t.home.title} description={t.home.description}>
  <!-- Hero Section -->
  <div class="hero min-h-screen bg-gradient-to-r from-primary to-secondary">
    <div class="hero-content text-center text-primary-content">
      <div class="max-w-md">
        <h1 class="mb-5 text-5xl font-bold">{t.home.hero.title}</h1>
        <p class="mb-5">
          {t.home.hero.subtitle}
        </p>
        <button class="btn btn-accent" onclick="document.getElementById('features').scrollIntoView({behavior: 'smooth'})">
          {t.home.hero.cta}
        </button>
      </div>
    </div>
  </div>

  <!-- Features Section -->
  <section id="features" class="py-20">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold mb-4">{t.home.features.title}</h2>
      <p class="text-lg text-base-content/70">{t.home.features.subtitle}</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Astro Card -->
      <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
        <figure class="px-10 pt-10">
          <div class="w-16 h-16 bg-gradient-to-r from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
            <Rocket class="w-8 h-8 text-white" />
          </div>
        </figure>
        <div class="card-body items-center text-center">
          <h3 class="card-title">{t.home.features.astro.title}</h3>
          <p>{t.home.features.astro.description}</p>
        </div>
      </div>

      <!-- Tailwind CSS Card -->
      <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
        <figure class="px-10 pt-10">
          <div class="w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
            <Palette class="w-8 h-8 text-white" />
          </div>
        </figure>
        <div class="card-body items-center text-center">
          <h3 class="card-title">{t.home.features.tailwind.title}</h3>
          <p>{t.home.features.tailwind.description}</p>
        </div>
      </div>

      <!-- daisyUI Card -->
      <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
        <figure class="px-10 pt-10">
          <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
            <Component class="w-8 h-8 text-white" />
          </div>
        </figure>
        <div class="card-body items-center text-center">
          <h3 class="card-title">{t.home.features.daisyui.title}</h3>
          <p>{t.home.features.daisyui.description}</p>
        </div>
      </div>

      <!-- Alpine.js Card -->
      <div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
        <figure class="px-10 pt-10">
          <div class="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
            <Zap class="w-8 h-8 text-white" />
          </div>
        </figure>
        <div class="card-body items-center text-center">
          <h3 class="card-title">{t.home.features.alpine.title}</h3>
          <p>{t.home.features.alpine.description}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Interactive Demo Section -->
  <section class="py-20 bg-base-200">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold mb-4">{t.home.demo.title}</h2>
      <p class="text-lg text-base-content/70">{t.home.demo.subtitle}</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
      <!-- Counter Demo -->
      <div class="card bg-base-100 shadow-xl" x-data="{ count: 0 }">
        <div class="card-body">
          <h3 class="card-title">{t.home.demo.counter.title}</h3>
          <div class="text-center py-8">
            <div class="text-6xl font-bold text-primary mb-4" x-text="count"></div>
            <div class="space-x-4">
              <button class="btn btn-error" @click="count--">-</button>
              <button class="btn btn-success" @click="count++">+</button>
              <button class="btn btn-warning" @click="count = 0">{t.home.demo.counter.reset}</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Todo Demo -->
      <div class="card bg-base-100 shadow-xl" x-data={`{
        todos: ${JSON.stringify(t.home.demo.todo.items)},
        newTodo: '',
        addTodo() {
          if (this.newTodo.trim()) {
            this.todos.push(this.newTodo.trim());
            this.newTodo = '';
          }
        },
        removeTodo(index) {
          this.todos.splice(index, 1);
        }
      }`}>
        <div class="card-body">
          <h3 class="card-title">{t.home.demo.todo.title}</h3>
          <div class="form-control">
            <div class="input-group">
              <input
                type="text"
                placeholder={t.home.demo.todo.placeholder}
                class="input input-bordered flex-1"
                x-model="newTodo"
                @keyup.enter="addTodo()"
              />
              <button class="btn btn-primary" @click="addTodo()">{t.home.demo.todo.add}</button>
            </div>
          </div>
          <div class="space-y-2 mt-4">
            <template x-for="(todo, index) in todos" :key="index">
              <div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                <span x-text="todo"></span>
                <button class="btn btn-sm btn-error" @click="removeTodo(index)">{t.home.demo.todo.delete}</button>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20">
    <div class="text-center">
      <h2 class="text-4xl font-bold mb-4">开始构建你的项目</h2>
      <p class="text-lg text-base-content/70 mb-8">使用这个强大的技术栈创建令人惊叹的网站</p>
      <div class="space-x-4">
        <button class="btn btn-primary btn-lg">开始使用</button>
        <button class="btn btn-outline btn-lg">查看文档</button>
      </div>
    </div>
  </section>
</Layout>
