---
import Layout from '../layouts/Layout.astro';

// Import Lucide icons
import { Info, CheckCircle, Target } from '@lucide/astro';

// Import i18n utilities
import { getCurrentLocale, getTranslations } from '../i18n';

// Get current locale and translations
const currentLocale = getCurrentLocale();
const t = getTranslations(currentLocale);
---

<Layout title={t.about.title} description={t.about.description}>
  <div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="text-center mb-16">
      <h1 class="text-5xl font-bold mb-4">{t.about.header.title}</h1>
      <p class="text-xl text-base-content/70">{t.about.header.subtitle}</p>
    </div>

    <!-- Technology Stack -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
      <div>
        <h2 class="text-3xl font-bold mb-6">{t.about.tech.title}</h2>
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-orange-400 to-red-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">A</span>
            </div>
            <div>
              <h3 class="font-semibold">{t.about.tech.astro.title}</h3>
              <p class="text-sm text-base-content/70">{t.about.tech.astro.description}</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">T</span>
            </div>
            <div>
              <h3 class="font-semibold">{t.about.tech.tailwind.title}</h3>
              <p class="text-sm text-base-content/70">{t.about.tech.tailwind.description}</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">D</span>
            </div>
            <div>
              <h3 class="font-semibold">{t.about.tech.daisyui.title}</h3>
              <p class="text-sm text-base-content/70">{t.about.tech.daisyui.description}</p>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">A</span>
            </div>
            <div>
              <h3 class="font-semibold">{t.about.tech.alpine.title}</h3>
              <p class="text-sm text-base-content/70">{t.about.tech.alpine.description}</p>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2 class="text-3xl font-bold mb-6">为什么选择这个技术栈？</h2>
        <div class="space-y-4">
          <div class="card bg-base-200">
            <div class="card-body">
              <h3 class="card-title text-lg">🚀 性能优异</h3>
              <p class="text-sm">Astro 的零 JavaScript 运行时确保了极快的加载速度</p>
            </div>
          </div>

          <div class="card bg-base-200">
            <div class="card-body">
              <h3 class="card-title text-lg">🎨 设计美观</h3>
              <p class="text-sm">Tailwind CSS + daisyUI 提供了现代化的设计系统</p>
            </div>
          </div>

          <div class="card bg-base-200">
            <div class="card-body">
              <h3 class="card-title text-lg">⚡ 开发高效</h3>
              <p class="text-sm">Alpine.js 让交互开发变得简单而直观</p>
            </div>
          </div>

          <div class="card bg-base-200">
            <div class="card-body">
              <h3 class="card-title text-lg">📱 响应式</h3>
              <p class="text-sm">完美适配各种设备和屏幕尺寸</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Features -->
    <div class="mb-16">
      <h2 class="text-3xl font-bold text-center mb-8">框架特性</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <Info class="w-8 h-8 text-primary-content" />
          </div>
          <h3 class="text-xl font-semibold mb-2">易于使用</h3>
          <p class="text-base-content/70">简单的配置，快速上手</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle class="w-8 h-8 text-secondary-content" />
          </div>
          <h3 class="text-xl font-semibold mb-2">生产就绪</h3>
          <p class="text-base-content/70">经过测试的稳定技术栈</p>
        </div>

        <div class="text-center">
          <div class="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
            <Target class="w-8 h-8 text-accent-content" />
          </div>
          <h3 class="text-xl font-semibold mb-2">可扩展</h3>
          <p class="text-base-content/70">支持各种插件和扩展</p>
        </div>
      </div>
    </div>
  </div>
</Layout>
