---
import Head from '../components/Head.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import { defaultLocale } from '../i18n';

// Import global styles
import '../styles/global.css';

export interface Props {
  title?: string;
  description?: string;
}

const currentLocale = defaultLocale;
const { title, description } = Astro.props;
---

<!DOCTYPE html>
<html lang={currentLocale === 'zh' ? 'zh-CN' : 'en'} data-theme="light">
  <Head title={title} description={description} />

  <body class="min-h-screen bg-base-100">
    <Header />

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
      <slot />
    </main>

    <Footer />

    <!-- External JavaScript -->
    <script is:inline src="../scripts/main.js"></script>
  </body>
</html>
