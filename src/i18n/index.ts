import { zh } from './zh';
import { en } from './en';

export type Locale = 'zh' | 'en';
export type LocalePreference = 'zh' | 'en' | 'system';

export const translations = {
  zh,
  en
} as const;

export const defaultLocale: Locale = 'zh';

export const locales = Object.keys(translations) as Locale[];

// Get translation function
export function getTranslations(locale: Locale = defaultLocale) {
  return translations[locale] || translations[defaultLocale];
}

// Get system language
export function getSystemLanguage(): Locale {
  if (typeof window !== 'undefined') {
    const browserLang = navigator.language.split('-')[0] as Locale;
    if (locales.includes(browserLang)) {
      return browserLang;
    }
  }
  return defaultLocale;
}

// Get current locale from localStorage or browser (client-side only)
export function getCurrentLocale(): Locale {
  if (typeof window !== 'undefined') {
    // Check localStorage first
    const savedLocale = localStorage.getItem('locale') as LocalePreference;
    if (savedLocale === 'system') {
      return getSystemLanguage();
    }
    if (savedLocale && locales.includes(savedLocale as Locale)) {
      return savedLocale as Locale;
    }

    // Default to system language
    return getSystemLanguage();
  }

  return defaultLocale;
}

// Get locale for server-side rendering (from request headers if available)
export function getServerLocale(request?: Request): Locale {
  if (request) {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      const browserLang = acceptLanguage.split(',')[0].split('-')[0] as Locale;
      if (locales.includes(browserLang)) {
        return browserLang;
      }
    }
  }
  return defaultLocale;
}

// Set locale
export function setLocale(locale: Locale) {
  if (typeof window !== 'undefined') {
    localStorage.setItem('locale', locale);

    // Reload page to apply new locale
    window.location.reload();
  }
}

// Get localized path (no URL parameters needed since we use localStorage)
export function getLocalizedPath(path: string, locale: Locale = defaultLocale): string {
  return path;
}

// Utility function to get nested translation
export function t(translations: any, key: string): string {
  const keys = key.split('.');
  let result = translations;

  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = result[k];
    } else {
      return key; // Return key if translation not found
    }
  }

  return typeof result === 'string' ? result : key;
}

// Language names for display
export const languageNames = {
  zh: '中文',
  en: 'English'
} as const;
