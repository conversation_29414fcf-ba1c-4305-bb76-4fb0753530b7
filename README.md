# 现代前端框架

一个使用 Astro + Tailwind CSS + daisyUI + Alpine.js 构建的现代化前端框架。

## ✨ 特性

- 🚀 **Astro** - 现代静态站点生成器，零 JavaScript 运行时
- 🎨 **Tailwind CSS** - 实用优先的 CSS 框架
- 🌈 **daisyUI** - 基于 Tailwind CSS 的美观组件库
- ⚡ **Alpine.js** - 轻量级 JavaScript 框架，简单的响应式交互
- 📱 **响应式设计** - 完美适配各种设备
- 🎯 **TypeScript 支持** - 类型安全的开发体验
- 📦 **pnpm** - 快速、节省磁盘空间的包管理器
- 🔧 **开发友好** - 热重载，快速开发

## 📦 关于 pnpm

本项目使用 [pnpm](https://pnpm.io/) 作为包管理器，相比 npm 和 yarn 具有以下优势：

- ⚡ **更快的安装速度** - 并行安装和智能缓存
- 💾 **节省磁盘空间** - 硬链接共享依赖，避免重复存储
- 🔒 **更严格的依赖管理** - 防止幽灵依赖问题
- 🌳 **扁平化 node_modules** - 更清晰的依赖结构

### 安装 pnpm

如果您还没有安装 pnpm，可以通过以下方式安装：

```bash
# 使用 npm 安装
npm install -g pnpm

# 或使用 curl 安装
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

## 🚀 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
# 本地开发
pnpm dev

# 允许外网访问（局域网/公网）
pnpm dev:host
```

访问地址：
- **本地访问**: [http://localhost:4321](http://localhost:4321)
- **外网访问**: http://[您的IP地址]:4321

> 💡 **提示**: 使用 `pnpm dev:host` 可以让局域网内的其他设备访问您的开发服务器

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
# 本地预览
pnpm preview

# 允许外网访问预览
pnpm preview:host
```

## 📁 项目结构

```text
/
├── public/                 # 静态资源
│   └── favicon.svg
├── src/
│   ├── components/         # 可重用组件
│   │   ├── Button.astro
│   │   └── Card.astro
│   ├── layouts/           # 布局组件
│   │   └── Layout.astro
│   ├── pages/             # 页面文件
│   │   ├── index.astro    # 首页
│   │   ├── about.astro    # 关于页面
│   │   └── contact.astro  # 联系页面
│   └── styles/            # 样式文件
│       └── global.css     # 全局样式
├── astro.config.mjs       # Astro 配置
├── tailwind.config.js     # Tailwind CSS 配置
├── tsconfig.json          # TypeScript 配置
└── package.json
```

## 🌐 外网访问配置

### 自动配置（推荐）

项目已经预配置为支持外网访问，只需使用以下命令：

```bash
# 开发环境外网访问
pnpm dev:host

# 生产预览外网访问
pnpm preview:host
```

### 手动配置

如果需要自定义配置，可以修改 `astro.config.mjs`：

```javascript
export default defineConfig({
  server: {
    host: true,        // 允许外网访问
    port: 4321        // 自定义端口
  }
});
```

### 获取访问地址

启动服务器后，终端会显示：
- **Local**: http://localhost:4321/ （本地访问）
- **Network**: http://[您的IP]:4321/ （外网访问）

### 安全提示

⚠️ **注意**: 外网访问模式会将开发服务器暴露给网络上的其他设备，请确保：
- 仅在受信任的网络环境中使用
- 生产环境请使用适当的安全配置
- 考虑使用防火墙限制访问

## 🎨 主题定制

### 更改 daisyUI 主题

在 `tailwind.config.js` 中修改主题配置：

```javascript
daisyui: {
  themes: ["light", "dark", "cupcake", "synthwave"],
  darkTheme: "dark",
}
```

### 自定义颜色

您可以在 Tailwind CSS 配置中添加自定义颜色：

```javascript
theme: {
  extend: {
    colors: {
      'custom-blue': '#1e40af',
    }
  }
}
```

## 🧩 组件使用

### Button 组件

```astro
---
import Button from '../components/Button.astro';
---

<Button variant="primary" size="lg">点击我</Button>
<Button variant="outline" href="/about">了解更多</Button>
```

### Card 组件

```astro
---
import Card from '../components/Card.astro';
---

<Card
  title="卡片标题"
  description="卡片描述"
  href="/details"
>
  <p>卡片内容</p>
</Card>
```

## 🔧 Alpine.js 交互

在任何 `.astro` 文件中使用 Alpine.js：

```astro
<div x-data="{ count: 0 }">
  <button @click="count++" class="btn btn-primary">
    点击次数: <span x-text="count"></span>
  </button>
</div>
```

## 📝 添加新页面

1. 在 `src/pages/` 目录下创建新的 `.astro` 文件
2. 使用 Layout 组件包装页面内容：

```astro
---
import Layout from '../layouts/Layout.astro';
---

<Layout title="新页面" description="页面描述">
  <h1>新页面内容</h1>
</Layout>
```

## 🛠️ 可用命令

| 命令 | 说明 |
| :--- | :--- |
| `pnpm install` | 安装依赖 |
| `pnpm dev` | 启动开发服务器（仅本地访问） |
| `pnpm dev:host` | 启动开发服务器（允许外网访问） |
| `pnpm build` | 构建生产版本 |
| `pnpm preview` | 预览生产版本（仅本地访问） |
| `pnpm preview:host` | 预览生产版本（允许外网访问） |
| `pnpm astro add` | 添加 Astro 集成 |

## 📚 学习资源

- [Astro 文档](https://docs.astro.build)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [daisyUI 文档](https://daisyui.com)
- [Alpine.js 文档](https://alpinejs.dev)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
