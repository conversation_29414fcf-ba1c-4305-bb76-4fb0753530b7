---
// This component handles client-side internationalization
---

<script>
  import { getCurrentLocale, getTranslations, languageNames } from '../i18n';

  // Translation data
  const translations = {
    zh: {
      nav: {
        home: '首页',
        about: '关于',
        contact: '联系',
        website: '我的网站'
      },
      theme: {
        light: '浅色主题',
        dark: '深色主题'
      },
      language: {
        chinese: '中文',
        english: 'English'
      },
      footer: {
        home: '首页',
        about: '关于',
        contact: '联系',
        privacy: '隐私政策',
        copyright: '版权所有'
      }
    },
    en: {
      nav: {
        home: 'Home',
        about: 'About',
        contact: 'Contact',
        website: 'My Website'
      },
      theme: {
        light: 'Light Theme',
        dark: 'Dark Theme'
      },
      language: {
        chinese: '中文',
        english: 'English'
      },
      footer: {
        home: 'Home',
        about: 'About',
        contact: 'Contact',
        privacy: 'Privacy',
        copyright: 'Copyright'
      }
    }
  };

  // Update page content based on current locale
  function updatePageContent() {
    const currentLocale = getCurrentLocale();
    const t = translations[currentLocale] || translations.zh;
    
    // Update HTML lang attribute
    document.documentElement.lang = currentLocale === 'zh' ? 'zh-CN' : 'en';
    
    // Update navigation links
    const navLinks = document.querySelectorAll('[data-i18n]');
    navLinks.forEach(link => {
      const key = link.getAttribute('data-i18n');
      if (key) {
        const keys = key.split('.');
        let value = t;
        for (const k of keys) {
          value = value[k];
        }
        if (value) {
          link.textContent = value;
        }
      }
    });
    
    // Update active language button
    const languageButtons = document.querySelectorAll('[data-lang-btn]');
    languageButtons.forEach(btn => {
      btn.classList.remove('active');
      if (btn.getAttribute('data-lang-btn') === currentLocale) {
        btn.classList.add('active');
      }
    });
  }

  // Get current locale from localStorage
  function getCurrentLocale() {
    const savedLocale = localStorage.getItem('locale');
    if (savedLocale && ['zh', 'en'].includes(savedLocale)) {
      return savedLocale;
    }
    
    // Check browser language as fallback
    const browserLang = navigator.language.split('-')[0];
    if (['zh', 'en'].includes(browserLang)) {
      return browserLang;
    }
    
    return 'zh'; // default locale
  }

  // Set locale and reload page
  function setLocale(locale) {
    localStorage.setItem('locale', locale);
    window.location.reload();
  }

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', updatePageContent);

  // Make setLocale available globally
  window.setLocale = setLocale;
</script>
