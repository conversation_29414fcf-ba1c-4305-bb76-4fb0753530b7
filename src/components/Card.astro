---
export interface Props {
  title: string;
  description?: string;
  image?: string;
  href?: string;
  class?: string;
}

const { title, description, image, href, class: className = '' } = Astro.props;
---

<div class={`card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow ${className}`}>
  {image && (
    <figure>
      <img src={image} alt={title} class="w-full h-48 object-cover" />
    </figure>
  )}
  <div class="card-body">
    <h2 class="card-title">{title}</h2>
    {description && <p class="text-base-content/70">{description}</p>}
    <slot />
    {href && (
      <div class="card-actions justify-end">
        <a href={href} class="btn btn-primary">了解更多</a>
      </div>
    )}
  </div>
</div>
