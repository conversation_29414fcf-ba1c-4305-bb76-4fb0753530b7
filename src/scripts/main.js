// Get system language
function getSystemLanguage() {
  const browserLang = navigator.language.split('-')[0];
  if (['zh', 'en'].includes(browserLang)) {
    return browserLang;
  }
  return 'zh'; // default fallback
}

// Get current locale from localStorage
function getCurrentLocale() {
  const savedLocale = localStorage.getItem('locale');
  if (savedLocale && ['zh', 'en', 'system'].includes(savedLocale)) {
    if (savedLocale === 'system') {
      return getSystemLanguage();
    }
    return savedLocale;
  }
  
  // Default to system detection
  return getSystemLanguage();
}

// Get saved locale preference (including 'system')
function getSavedLocalePreference() {
  const savedLocale = localStorage.getItem('locale');
  if (savedLocale && ['zh', 'en', 'system'].includes(savedLocale)) {
    return savedLocale;
  }
  return 'system'; // default to system
}

// Set locale and reload page
function setLocale(locale) {
  console.log('Setting locale to:', locale);
  localStorage.setItem('locale', locale);
  window.location.reload();
}

// Theme management
function getSystemTheme() {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

function getCurrentTheme() {
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
    return savedTheme;
  }
  return 'system'; // default to system
}

function applyTheme(theme) {
  let actualTheme = theme;
  if (theme === 'system') {
    actualTheme = getSystemTheme();
  }
  document.documentElement.setAttribute('data-theme', actualTheme);
}

function setTheme(theme) {
  console.log('Setting theme to:', theme);
  localStorage.setItem('theme', theme);
  applyTheme(theme);
  updateThemeButtons();
}

function updateThemeButtons() {
  const currentTheme = getCurrentTheme();
  const themeButtons = document.querySelectorAll('[data-theme-btn]');
  themeButtons.forEach(btn => {
    btn.classList.remove('active');
    if (btn.getAttribute('data-theme-btn') === currentTheme) {
      btn.classList.add('active');
    }
  });
}

// Make functions available globally
window.setLocale = setLocale;
window.setTheme = setTheme;

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
  const currentLocale = getCurrentLocale();
  const savedPreference = getSavedLocalePreference();
  console.log('Current locale:', currentLocale, 'Saved preference:', savedPreference);
  
  // Update HTML lang attribute
  document.documentElement.lang = currentLocale === 'zh' ? 'zh-CN' : 'en';
  
  // Update active language button (show the preference, not the actual locale)
  const languageButtons = document.querySelectorAll('[data-lang-btn]');
  languageButtons.forEach(btn => {
    btn.classList.remove('active');
    if (btn.getAttribute('data-lang-btn') === savedPreference) {
      btn.classList.add('active');
    }
  });
  
  // Initialize theme
  const currentTheme = getCurrentTheme();
  applyTheme(currentTheme);
  updateThemeButtons();
  
  // Listen for system theme changes
  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function() {
    const currentTheme = getCurrentTheme();
    if (currentTheme === 'system') {
      applyTheme('system');
    }
  });
});
